'use client';

import { ElementType, useState } from 'react';
import { redirect, usePathname } from 'next/navigation';
import { CaretLeft, IconProps } from '@phosphor-icons/react';

type SidebarItemProps = {
  href?: string;
  icon?: ElementType<IconProps>;
  label: string;
  items?: SidebarItemProps[];
  onClick?: () => void;
};

const SidebarItem = ({
  href,
  icon: Icon,
  label,
  items,
  onClick
}: SidebarItemProps) => {
  const pathname = usePathname();
  const isActive = pathname === href;
  const [isOpen, setIsOpen] = useState<boolean>(pathname.includes(href!));

  const handleClick = () => {
    if (items && items.length) {
      setIsOpen(!isOpen);
      return;
    }

    if (onClick) {
      onClick();
      return;
    }

    if (href) {
      redirect(href);
    }
  };

  return (
    <div
      onClick={handleClick}
      className={`
        flex flex-col px-4 py-3 rounded-inner
        transition-colors duration-200 cursor-pointer
        ${
          isActive
            ? 'bg-gray-100 text-primary-500'
            : 'hover:bg-gray-100 text-gray-500'
        }
      `}
    >
      <div className='flex items-center justify-between'>
        <div className='flex gap-3 items-center'>
          {Icon && (
            <div className='flex-none text-gray-500 items-center justify-center'>
              <Icon size={18} />
            </div>
          )}
          <h3 className='font-medium leading-4 text-sm text-primary-900'>
            {label}
          </h3>
        </div>
        {items && items.length && (
          <CaretLeft
            className={`transition-all ${isOpen ? '-rotate-90' : ''}`}
          />
        )}
      </div>
      {items && items.length && isOpen && (
        <div className='flex flex-col mt-3'>
          {items.map(item => (
            <SidebarItem key={item.href} href={item.href} label={item.label} />
          ))}
        </div>
      )}
    </div>
  );
};

export default SidebarItem;
