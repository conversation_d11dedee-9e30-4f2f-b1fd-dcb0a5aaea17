'use client';

import { loadInvoices } from '@/services/invoice';
import { Button } from '@ourtrip/ui';
import { Calendar, FileCsv, Upload, X } from '@phosphor-icons/react';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

const SendNFe = () => {
  const router = useRouter();
  const [isSending, setIsSending] = useState<boolean>(false);
  const { register, handleSubmit, watch, reset } = useForm<{
    file: FileList | null;
    competence: Date;
  }>({
    defaultValues: {
      file: null,
      competence: new Date()
    }
  });

  const file = watch('file');

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      reset({ file: files });
    }
  };

  const handleSend = async (values: {
    file: FileList | null;
    competence: Date;
  }) => {
    setIsSending(true);
    const formData = new FormData();

    formData.append('file', values.file![0], values.file![0].name);
    formData.append(
      'competencia',
      format(values.competence || new Date(), 'yyyy-MM-dd')
    );

    const response = await loadInvoices(formData);
    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Notas fiscais enviadas com sucesso');
      reset({ file: null, competence: new Date() });
      router.refresh();
    }

    setIsSending(false);
  };

  return (
    <form
      className='w-[360px] flex-none flex flex-col gap-2 bg-white rounded-default p-4 overflow-hidden'
      onSubmit={handleSubmit(handleSend)}
    >
      <p className='text-sm text-gray-500'>Enviar NFs</p>
      {!file?.[0] && (
        <div
          className='w-full border-2 border-dashed border-gray-200 rounded-inner p-6 text-center cursor-pointer hover:border-gray-400 transition-colors'
          onDrop={handleDrop}
          onDragOver={e => e.preventDefault()}
          onClick={() => document.getElementById('fileInput')?.click()}
          role='button'
          tabIndex={0}
          onKeyDown={e => {
            if (e.key === 'Enter' || e.key === ' ') {
              document.getElementById('fileInput')?.click();
            }
          }}
          aria-label='Clique ou arraste o arquivo para enviar'
        >
          <Upload size={22} className='mx-auto text-gray-500' />
          <p className='mt-2 text-sm text-gray-500'>
            Clique ou arraste o arquivo para enviar
          </p>
        </div>
      )}
      <input
        id='fileInput'
        {...register('file')}
        type='file'
        className='hidden'
        multiple={false}
        accept='text/tab-separated-values'
      />
      <input
        id='dateInput'
        type='date'
        {...register('competence')}
        className='hidden'
      />
      {file?.[0] && (
        <div className='w-full flex items-center gap-2 overflow-hidden'>
          <div className='w-full bg-gray-100 rounded-inner flex items-center justify-between gap-2 p-3 overflow-hidden'>
            <div className='flex items-center gap-2'>
              <FileCsv size={22} className='flex-none text-gray-500' />
              <p className='text-sm text-primary-900 line-clamp-1'>
                {file![0].name || ''}
              </p>
            </div>
            <X
              size={18}
              className='text-gray-500 cursor-pointer flex-none'
              onClick={() => reset()}
            />
          </div>
          <div className='bg-gray-100 rounded-inner flex items-center justify-between gap-2 p-3 cursor-pointer hover:bg-gray-200'>
            <Calendar size={18} className='flex-none' />
          </div>
        </div>
      )}
      <Button
        color='primary'
        className='w-full'
        type='submit'
        loading={isSending}
        disabled={!file?.[0]}
      >
        Enviar
      </Button>
    </form>
  );
};

export default SendNFe;
