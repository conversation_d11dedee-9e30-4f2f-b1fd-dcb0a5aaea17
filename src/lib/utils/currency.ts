/* eslint-disable no-unused-vars */
/* eslint-disable no-shadow */
export enum PaymentMethodIdEnum {
  mastercard = 'Mastercard',
  visa = 'Visa',
  amex = 'American Express',
  pix = 'Pix',
  elo = 'Elo'
}

export const convertNumberToCurrency = (currency: string, value: number) => {
  const formatter = new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currencyDisplay: 'code',
    currency
  });

  return formatter
    .format(value / 100)
    .replace(currency, '')
    .trim();
};
