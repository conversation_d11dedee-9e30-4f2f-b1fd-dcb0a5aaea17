/* eslint-disable import/no-duplicates */
import { format, Locale } from 'date-fns';
import { enUS, es, pt, ptBR } from 'date-fns/locale';
import { capitalizeFirstLetter } from './strings';

export const convertStringToUTCDate = (data: string) => {
  const date = new Date(data);
  return data.includes('T')
    ? new Date(data)
    : new Date(date.valueOf() + date.getTimezoneOffset() * 60 * 1000);
};

export const convertStringToDefaultFormatedDate = (date: string) => {
  const convertedDate = convertStringToUTCDate(date);
  return format(convertedDate, 'dd/MM/yyyy', {
    locale: ptBR
  });
};

export const convertStringToDateWithMask = (date: string, mask: string) => {
  const convertedDate = convertStringToUTCDate(date);
  return format(convertedDate, mask, {
    locale: ptBR
  });
};

export const convertStringToFormatedDateByLocale = (
  value: string,
  locale: Locale
) => {
  if (!value || value === '') return '';

  const date = convertStringToUTCDate(value);
  const day = capitalizeFirstLetter(format(date, 'PPPP', { locale }));

  return `${day}`;
};

export const convertStringToFormatedDate = (value: string) => {
  const date = convertStringToUTCDate(value);

  const dayOfWeek = capitalizeFirstLetter(
    format(date, 'EEEEEE', { locale: ptBR })
  );
  const dayOfMonth = format(date, 'dd', { locale: ptBR });
  const month = capitalizeFirstLetter(format(date, 'MMMM', { locale: ptBR }));
  const year = format(date, 'yyyy', { locale: ptBR });

  return `${dayOfWeek}, ${dayOfMonth} de ${month}, ${year}`;
};

export const convertStringToFormatedDateWithDate = (value: string) => {
  const date = convertStringToUTCDate(value);

  const dayOfWeek = capitalizeFirstLetter(
    format(date, 'EEEEEE', { locale: ptBR })
  );
  const dayOfMonth = format(date, 'dd', { locale: ptBR });
  const month = capitalizeFirstLetter(format(date, 'MMMM', { locale: ptBR }));
  const year = format(date, 'yyyy', { locale: ptBR });
  const time = format(date, 'HH:mm:ss', { locale: ptBR });

  return `${dayOfWeek}, ${dayOfMonth} de ${month}, ${year} às ${time}`;
};

export const getDateFNSLocale = (locale: string) => {
  switch (locale.toUpperCase()) {
    case 'PT_BR':
      return ptBR;
    case 'PT_PT':
      return pt;
    case 'EN_US':
      return enUS;
    case 'ES_ES':
      return es;
    default:
      return ptBR;
  }
};

export const timeSince = (date: number) => {
  const seconds = Math.floor((new Date().getTime() - date) / 1000);

  let interval = seconds / 31536000;

  if (interval > 1) {
    return `há ${Math.floor(interval)} ${
      Math.floor(interval) > 1 ? 'anos' : 'ano'
    } atrás`;
  }
  interval = seconds / 2592000;
  if (interval > 1) {
    return `há ${Math.floor(interval)} ${
      Math.floor(interval) > 1 ? 'meses' : 'mês'
    } atrás`;
  }
  interval = seconds / 86400;
  if (interval > 1) {
    return `há ${Math.floor(interval)} ${
      Math.floor(interval) > 1 ? 'dias' : 'dia'
    } atrás`;
  }
  interval = seconds / 3600;
  if (interval > 1) {
    return `há ${Math.floor(interval)} ${
      Math.floor(interval) > 1 ? 'horas' : 'hora'
    } atrás`;
  }
  interval = seconds / 60;
  if (interval > 1) {
    return `há ${Math.floor(interval)} ${
      Math.floor(interval) > 1 ? 'minutes' : 'minuto'
    } atrás`;
  }
  return `há ${Math.floor(seconds)} ${
    Math.floor(interval) > 1 ? 'segundos' : 'segundo'
  } atrás`;
};

export const convertSecondsToMinutes = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  const formatedMinutes = minutes < 10 ? `0${minutes}` : minutes;
  const formatedSeconds =
    remainingSeconds < 10 ? `0${remainingSeconds}` : remainingSeconds;

  return `${formatedMinutes}:${formatedSeconds}`;
};
