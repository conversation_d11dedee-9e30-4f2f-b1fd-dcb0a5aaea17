import RefreshButton from '@/components/refresh-button';
import TimiPlay from '@/components/timi-play';
import TimiRunList from '@/components/timi-run-list';
import TimiRunning from '@/components/timi-running';
import TimiStatus from '@/components/timi-status';
import { createClient } from '@/services/supabase';

const Timi = async () => {
  const supabase = await createClient();
  const { data: runs } = await supabase
    .from('run')
    .select('*')
    .not('ended_at', 'is', null)
    .order('created_at', { ascending: false });

  const { data: running } = await supabase
    .from('run')
    .select('*')
    .is('ended_at', null)
    .single();

  return (
    <div className='w-full flex flex-col gap-4 py-12 pr-12 relative overflow-auto scrollbar'>
      <div className='flex items-end gap-2'>
        <h1 className='text-xl font-semibold'>Timi</h1>
        <RefreshButton />
      </div>
      <div className='w-full flex gap-6'>
        <div className='w-[300px] flex flex-col gap-3 flex-none'>
          <TimiStatus />
          {running && <TimiRunning initialRun={running} />}
          <TimiRunList runs={runs ?? []} />
        </div>
        <div className='w-full flex flex-col gap-3'>
          {!running && <TimiPlay />}
        </div>
      </div>
    </div>
  );
};

export default Timi;
