import RefreshButton from '@/components/refresh-button';
import ReservationCard from '@/components/reservation-card';
import { convertStringToDefaultFormatedDate } from '@/lib/utils/date';
import { getOrder } from '@/services/order';
import OrderPayment from '@/components/order-payment';
import OrderCancelState from '@/components/order-cancel-state';
import OrderActions from '@/components/order-actions';
import OrderCustomer from '@/components/order-customer';
import OrderBackButton from '@/components/order-back-button';

const Order = async ({ params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params;
  const response = await getOrder(id);

  if (response.error) {
    throw new Error(response.error);
  }

  const order = response.data!;

  return (
    <div className='w-full flex flex-col gap-4 py-12 pr-12 overflow-auto scrollbar'>
      <OrderBackButton />
      <div className='flex items-end gap-2'>
        <h1 className='text-xl font-semibold'>Pedido {order.orderCode}</h1>
        <RefreshButton />
      </div>
      <div className='flex gap-4 justify-between'>
        <div className='flex-10 flex flex-col gap-4'>
          {order.itens.map(item => (
            <ReservationCard
              key={item.id}
              address={item.booking.hotel.address}
              channel={item.booking.channel}
              checkin={convertStringToDefaultFormatedDate(item.booking.checkin)}
              checkout={convertStringToDefaultFormatedDate(
                item.booking.checkout
              )}
              hotelId={item.booking.hotel.id}
              distributionText={item.booking.price.price.description}
              name={item.booking.hotel.name}
              rooms={item.booking.rooms}
              image={
                item.booking.hotel?.photoCover?.url ||
                '/images/hotel-placeholder.webp'
              }
              providerAccount={item.booking.providerAccount}
              reference={item.booking.reference}
              reservationCode={item.booking.reservationCode}
              stars={parseFloat(item.booking.hotel.stars)}
              status={item.booking.status}
              statusDisplay={item.booking.statusDisplay}
              currencySymbol={item.booking.price.currencySymbol}
              totalPrice={item.booking.price.price.formattedValue}
              backgroundColor='white'
              border={false}
            />
          ))}
          <OrderPayment order={order} />
        </div>
        <div className='flex-5 flex flex-col gap-4'>
          <OrderCancelState order={order} />
          <OrderActions order={order} />
          <OrderCustomer order={order} />
        </div>
      </div>
    </div>
  );
};

export default Order;
